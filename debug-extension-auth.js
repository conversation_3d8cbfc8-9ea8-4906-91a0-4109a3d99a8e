#!/usr/bin/env node

/**
 * Debug script to test extension authentication and usage tracking
 */

const https = require('https');
const http = require('http');

// Configuration
const API_BASE_URL = 'https://app.cubent.dev';
const TEST_TOKEN = process.env.TEST_TOKEN; // You'll need to provide a valid token

console.log('🔍 Debugging Extension Authentication and Usage Tracking\n');

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Cubent-Extension-Debug/1.0',
        ...options.headers
      }
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(typeof options.body === 'string' ? options.body : JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// Test functions
async function testAuthEndpoint() {
  console.log('📋 Testing Authentication Endpoint...');
  
  if (!TEST_TOKEN) {
    console.log('❌ No TEST_TOKEN provided. Please set TEST_TOKEN environment variable.');
    return false;
  }

  try {
    const response = await makeRequest(`${API_BASE_URL}/api/extension/auth`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });

    console.log(`   Status: ${response.status}`);
    console.log(`   Response:`, JSON.stringify(response.data, null, 2));

    if (response.status === 200 && response.data.user) {
      console.log('✅ Authentication endpoint working');
      return response.data.user;
    } else {
      console.log('❌ Authentication failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Authentication request failed:', error.message);
    return false;
  }
}

async function testUsageTrackingEndpoint(user) {
  console.log('\n📊 Testing Usage Tracking Endpoint...');
  
  if (!TEST_TOKEN) {
    console.log('❌ No TEST_TOKEN provided');
    return false;
  }

  const testUsageData = {
    modelId: 'claude-3-5-sonnet-20241022',
    provider: 'anthropic',
    configName: 'test-config',
    cubentUnits: 5,
    tokensUsed: 1000,
    inputTokens: 500,
    outputTokens: 500,
    costAccrued: 0.015,
    requestsMade: 1,
    timestamp: Date.now(),
    sessionId: 'test-session-' + Date.now(),
    metadata: {
      feature: 'chat',
      language: 'javascript'
    }
  };

  try {
    const response = await makeRequest(`${API_BASE_URL}/api/extension/track-usage`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      },
      body: testUsageData
    });

    console.log(`   Status: ${response.status}`);
    console.log(`   Response:`, JSON.stringify(response.data, null, 2));

    if (response.status === 200) {
      console.log('✅ Usage tracking endpoint working');
      return true;
    } else {
      console.log('❌ Usage tracking failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Usage tracking request failed:', error.message);
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('\n🗄️  Testing Database Connection...');
  
  // This would require database credentials, so we'll skip for now
  console.log('⏭️  Skipping database test (requires credentials)');
  return true;
}

async function runDiagnostics() {
  console.log(`API Base URL: ${API_BASE_URL}`);
  console.log(`Test Token: ${TEST_TOKEN ? 'Provided' : 'Not provided'}\n`);

  // Test authentication
  const user = await testAuthEndpoint();
  
  if (user) {
    // Test usage tracking
    await testUsageTrackingEndpoint(user);
  }

  // Test database connection
  await testDatabaseConnection();

  console.log('\n📋 Diagnostic Summary:');
  console.log('1. Check VS Code extension logs for authentication errors');
  console.log('2. Verify that the extension is using the correct API URL');
  console.log('3. Ensure the auth token is being properly stored and retrieved');
  console.log('4. Check if usage tracking is being called after successful API requests');
  
  console.log('\n💡 Debugging Tips:');
  console.log('- Enable debug logging in the extension');
  console.log('- Check the browser network tab when using the web app');
  console.log('- Verify the Neon database connection and table structure');
  console.log('- Test with a fresh authentication flow');

  console.log('\n🔧 Extension Installation:');
  console.log('✅ Extension has been built and installed successfully!');
  console.log('📦 Version: 0.30.0');
  console.log('📍 Location: bin/cubent-0.30.0.vsix');

  console.log('\n🚀 Next Steps:');
  console.log('1. Restart VS Code to ensure the extension loads properly');
  console.log('2. Open the Cubent extension in VS Code');
  console.log('3. Authenticate with your account');
  console.log('4. Make some API requests (chat with AI, use tools, etc.)');
  console.log('5. Check the VS Code Developer Console (Help > Toggle Developer Tools)');
  console.log('6. Look for console messages like:');
  console.log('   - "Standalone authentication and usage tracking services initialized"');
  console.log('   - "Tracked usage: X Cubent Units for model-name"');
  console.log('   - "Comprehensive usage tracking:" messages');
  console.log('7. Check your Neon database for new usage records');

  console.log('\n📊 What Should Happen Now:');
  console.log('- After authentication, usage data should be sent to Neon DB');
  console.log('- Each API request should generate usage tracking entries');
  console.log('- You should see Cubent Units, tokens, and costs being tracked');
  console.log('- The UserManagementIntegration should be active and working');
}

// Run diagnostics
runDiagnostics().catch(error => {
  console.error('💥 Diagnostic script failed:', error);
  process.exit(1);
});
